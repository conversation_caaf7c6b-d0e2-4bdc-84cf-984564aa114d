// API Base URL
export const API_BASE_URL =
  "https://cinema-resavation-system-production-6ffc.up.railway.app";

// Auth endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: "/api/auth/login",
  REGISTER: "/api/auth/register",
  LOGOUT: "/api/auth/logout",
  REFRESH_TOKEN: "/api/auth/refresh",
  SEND_OTP: "/api/auth/send-otp",
  VERIFY_OTP: "/api/auth/verify-otp",
};

// User endpoints
export const USER_ENDPOINTS = {
  GET_ALL: "/api/user/all",
  GET_BY_ID: "/api/user",
  ADD: "/api/user/add",
  UPDATE: "/api/user",
  DELETE: "/api/user",
};

// Movie endpoints
export const MOVIE_ENDPOINTS = {
  GET_ALL: "/api/phim",
  GET_BY_ID: "/api/phim",
  ADD: "/api/phim/add",
  UPDATE: "/api/phim",
  DELETE: "/api/phim",
};

// Room endpoints
export const ROOM_ENDPOINTS = {
  GET_ALL: "/api/room/all",
  GET_BY_ID: "/api/room",
  ADD: "/api/room/add",
  UPDATE: "/api/room",
  DELETE: "/api/room",
};

// Branch endpoints
export const BRANCH_ENDPOINTS = {
  GET_ALL: "/api/branch/all",
  GET_BY_ID: "/api/branch",
  ADD: "/api/branch/add",
  UPDATE: "/api/branch",
  DELETE: "/api/branch",
};

// Seat endpoints
export const SEAT_ENDPOINTS = {
  GET_ALL: "/api/seat/all",
  GET_BY_ID: "/api/seat",
  ADD: "/api/seat/add",
  UPDATE: "/api/seat",
  DELETE: "/api/seat",
};

// Schedule endpoints
export const SCHEDULE_ENDPOINTS = {
  GET_ALL: "/api/schedule/all",
  GET_BY_ID: "/api/schedule",
  ADD: "/api/schedule/add",
  UPDATE: "/api/schedule",
  DELETE: "/api/schedule",
};

// Bill endpoints
export const BILL_ENDPOINTS = {
  GET_ALL: "/api/bill/all",
  GET_BY_ID: "/api/bill",
  ADD: "/api/bill/add",
  UPDATE: "/api/bill",
  DELETE: "/api/bill",
};

// Invoice endpoints
export const INVOICE_ENDPOINTS = {
  GET_ALL: "/api/invoice/all",
  GET_BY_ID: "/api/invoice",
  ADD: "/api/invoice/add",
  UPDATE: "/api/invoice",
  DELETE: "/api/invoice",
};

// Showtime endpoints for modal
export const SHOWTIME_ENDPOINTS = {
  GET_BY_MOVIE: "/api/showtimes/movie",
  GET_BY_CINEMA: "/api/showtimes/cinema",
  GET_AVAILABLE_DATES: "/api/showtimes/dates",
  GET_CINEMAS: "/api/cinemas",
  GET_MOVIE_SHOWTIMES: "/api/showtimes/movie/{movieId}",
};

// Cinema endpoints
export const CINEMA_ENDPOINTS = {
  GET_ALL: "/api/cinemas",
  GET_BY_ID: "/api/cinemas/{id}",
  GET_ROOMS: "/api/cinemas/{id}/rooms",
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
};

// User Roles
export const USER_ROLES = {
  ADMIN: "admin",
  USER: "user",
  STAFF: "staff",
};
