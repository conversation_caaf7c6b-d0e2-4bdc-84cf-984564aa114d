<template>
  <div v-if="visible" class="modal-backdrop" @click.self="$emit('close')">
    <div class="cinema-modal">
      <button class="modal-close" @click="$emit('close')">×</button>
      
      <!-- Header -->
      <div class="modal-header">
        <h2 class="modal-title">CHỌN RẠP CHIẾU</h2>
        <p class="modal-subtitle">{{ movie?.tenPhim || movie?.title }}</p>
      </div>

      <!-- Cinema List -->
      <div class="cinema-list">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p><PERSON>ang tải danh sách rạp...</p>
        </div>
        
        <div v-else-if="error" class="error-state">
          <p>{{ error }}</p>
          <button @click="loadCinemas" class="retry-btn">Thử lại</button>
        </div>
        
        <div v-else class="cinema-grid">
          <button 
            v-for="cinema in cinemas" 
            :key="cinema.id"
            :class="['cinema-card', { selected: selectedCinema?.id === cinema.id }]"
            @click="selectCinema(cinema)"
          >
            <div class="cinema-icon">🏢</div>
            <div class="cinema-info">
              <h3 class="cinema-name">{{ cinema.name }}</h3>
              <p class="cinema-address">{{ cinema.address }}</p>
              <p v-if="cinema.phone" class="cinema-phone">📞 {{ cinema.phone }}</p>
              <div v-if="cinema.showtimeCount" class="showtime-count">
                {{ cinema.showtimeCount }} suất chiếu
              </div>
            </div>
            <div class="cinema-arrow">→</div>
          </button>
        </div>
      </div>

      <!-- Actions -->
      <div class="modal-actions">
        <button 
          class="btn-continue" 
          :disabled="!selectedCinema"
          @click="confirmSelection"
        >
          Xem lịch chiếu
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { fetchCinemas } from '../services/cinemaService'

const props = defineProps({
  movie: { type: Object, required: true },
  visible: { type: Boolean, default: false }
})

const emit = defineEmits(['close', 'selectCinema'])

const cinemas = ref([])
const selectedCinema = ref(null)
const loading = ref(false)
const error = ref('')

// Load cinemas when modal opens
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadCinemas()
    selectedCinema.value = null
  }
})

async function loadCinemas() {
  loading.value = true
  error.value = ''
  
  try {
    const response = await fetchCinemas(props.movie.idPhim || props.movie.id)
    cinemas.value = response.data || []
  } catch (err) {
    error.value = 'Không thể tải danh sách rạp. Vui lòng thử lại.'
    console.error('Error loading cinemas:', err)
  } finally {
    loading.value = false
  }
}

function selectCinema(cinema) {
  selectedCinema.value = cinema
}

function confirmSelection() {
  if (selectedCinema.value) {
    emit('selectCinema', {
      movie: props.movie,
      cinema: selectedCinema.value
    })
    emit('close')
  }
}
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.cinema-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  padding: 0;
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  z-index: 10;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #000;
}

.modal-header {
  padding: 25px 60px 20px 25px;
  border-bottom: 1px solid #eee;
  text-align: center;
}

.modal-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin: 0 0 8px 0;
  text-transform: uppercase;
}

.modal-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
  font-weight: 500;
}

.cinema-list {
  padding: 20px;
  min-height: 200px;
}

.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 10px;
}

.cinema-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cinema-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  width: 100%;
}

.cinema-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.cinema-card.selected {
  border-color: #007bff;
  background: #f8f9ff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

.cinema-icon {
  font-size: 32px;
  margin-right: 16px;
  flex-shrink: 0;
}

.cinema-info {
  flex: 1;
}

.cinema-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
}

.cinema-address {
  font-size: 14px;
  color: #666;
  margin: 0 0 4px 0;
}

.cinema-phone {
  font-size: 13px;
  color: #888;
  margin: 0 0 8px 0;
}

.showtime-count {
  font-size: 12px;
  color: #007bff;
  font-weight: 600;
  background: #e7f3ff;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.cinema-arrow {
  font-size: 20px;
  color: #007bff;
  margin-left: 16px;
  flex-shrink: 0;
}

.modal-actions {
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.btn-continue {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.btn-continue:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
}

.btn-continue:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive */
@media (max-width: 768px) {
  .cinema-modal {
    margin: 10px;
    max-height: 95vh;
  }
  
  .modal-title {
    font-size: 18px;
  }
  
  .cinema-card {
    padding: 12px;
  }
  
  .cinema-icon {
    font-size: 28px;
    margin-right: 12px;
  }
  
  .cinema-name {
    font-size: 16px;
  }
}
</style>
