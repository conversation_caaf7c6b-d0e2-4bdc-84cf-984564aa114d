<template>
  <div class="terms-page">
    <!-- Hero Section -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-content">
          <div class="hero-icon">
            <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M10 9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h1 class="hero-title">Điều khoản sử dụng</h1>
          <p class="hero-subtitle">Quy định và điều kiện sử dụng dịch vụ</p>
        </div>
      </div>
    </div>

    <!-- Content Section -->
    <div class="content-container">
      <div class="content-wrapper">
        <!-- General Terms Section -->
        <div class="terms-section">
          <div class="section-header">
            <div class="section-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h2>Quy định chung</h2>
          </div>
          <div class="section-content">
            <div class="terms-item">
              <div class="item-icon">⚖️</div>
              <div class="item-text">
                <h3>Tuân thủ pháp luật</h3>
                <p>Không sử dụng website cho mục đích vi phạm pháp luật hoặc các hoạt động bất hợp pháp.</p>
              </div>
            </div>
            <div class="terms-item">
              <div class="item-icon">📋</div>
              <div class="item-text">
                <h3>Bản quyền nội dung</h3>
                <p>Không sao chép, phát tán nội dung khi chưa được phép từ chủ sở hữu.</p>
              </div>
            </div>
            <div class="terms-item">
              <div class="item-icon">🔒</div>
              <div class="item-text">
                <h3>Bảo mật thông tin</h3>
                <p>Thông tin cá nhân của bạn sẽ được bảo mật theo chính sách của chúng tôi.</p>
              </div>
            </div>
            <div class="terms-item">
              <div class="item-icon">🔄</div>
              <div class="item-text">
                <h3>Quyền thay đổi</h3>
                <p>DEV CINEMA có quyền thay đổi nội dung, giao diện mà không cần báo trước.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Privacy & Security Section -->
        <div class="terms-section">
          <div class="section-header">
            <div class="section-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L3 7V12C3 16.4183 6.58172 20 11 20H13C17.4183 20 21 16.4183 21 12V7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M12 9L15 12L12 15L9 12L12 9Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
            </div>
            <h2>Bảo mật & quyền riêng tư</h2>
          </div>
          <div class="privacy-grid">
            <div class="privacy-card">
              <div class="card-icon">🛡️</div>
              <h3>Bảo vệ thông tin</h3>
              <p>Chúng tôi cam kết bảo vệ thông tin cá nhân của bạn</p>
            </div>
            <div class="privacy-card">
              <div class="card-icon">🎯</div>
              <h3>Sử dụng có mục đích</h3>
              <p>Thông tin chỉ được sử dụng cho mục đích phục vụ khách hàng</p>
            </div>
            <div class="privacy-card">
              <div class="card-icon">✏️</div>
              <h3>Quyền chỉnh sửa</h3>
              <p>Bạn có quyền yêu cầu chỉnh sửa hoặc xóa thông tin cá nhân</p>
            </div>
          </div>
        </div>

        <!-- User Responsibilities Section -->
        <div class="terms-section">
          <div class="section-header">
            <div class="section-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <h2>Trách nhiệm người dùng</h2>
          </div>
          <div class="responsibilities-list">
            <div class="responsibility-item">
              <div class="check-icon">✓</div>
              <span>Cung cấp thông tin chính xác khi đăng ký</span>
            </div>
            <div class="responsibility-item">
              <div class="check-icon">✓</div>
              <span>Bảo mật thông tin tài khoản cá nhân</span>
            </div>
            <div class="responsibility-item">
              <div class="check-icon">✓</div>
              <span>Tuân thủ các quy định khi sử dụng dịch vụ</span>
            </div>
            <div class="responsibility-item">
              <div class="check-icon">✓</div>
              <span>Báo cáo các hoạt động bất thường</span>
            </div>
          </div>
        </div>

        <!-- Contact Section -->
        <div class="contact-section">
          <div class="contact-card">
            <div class="contact-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2"/>
                <path d="M22 6L12 13L2 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3>Liên hệ với chúng tôi</h3>
            <p>Nếu có thắc mắc về điều khoản sử dụng, vui lòng liên hệ:</p>
            <a href="mailto:<EMAIL>" class="contact-email">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2"/>
                <path d="M22 6L12 13L2 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.terms-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.hero-background {
  position: relative;
  height: 100%;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  text-align: center;
  z-index: 1;
  position: relative;
}

.hero-icon {
  margin-bottom: 20px;
  color: #ffffff;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 16px;
  background: linear-gradient(45deg, #ffffff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
}

/* Content Container */
.content-container {
  padding: 60px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.content-wrapper {
  display: grid;
  gap: 40px;
}

/* Terms Section */
.terms-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.section-icon {
  color: #f093fb;
  background: rgba(240, 147, 251, 0.1);
  padding: 12px;
  border-radius: 12px;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.section-content {
  display: grid;
  gap: 24px;
}

.terms-item {
  display: flex;
  gap: 20px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.terms-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.item-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.item-text h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #f093fb;
}

.item-text p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

/* Privacy Grid */
.privacy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.privacy-card {
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.1), rgba(245, 87, 108, 0.1));
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  border: 1px solid rgba(240, 147, 251, 0.2);
  transition: all 0.3s ease;
}

.privacy-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(240, 147, 251, 0.2);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.privacy-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #f093fb;
}

.privacy-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin: 0;
}

/* Responsibilities List */
.responsibilities-list {
  display: grid;
  gap: 16px;
  margin-top: 24px;
}

.responsibility-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.responsibility-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(8px);
}

.check-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: #ffffff;
  border-radius: 50%;
  font-weight: bold;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.responsibility-item span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-weight: 500;
}

/* Contact Section */
.contact-section {
  text-align: center;
}

.contact-card {
  background: linear-gradient(135deg, rgba(240, 147, 251, 0.15), rgba(245, 87, 108, 0.15));
  border-radius: 20px;
  padding: 48px 32px;
  border: 1px solid rgba(240, 147, 251, 0.3);
  backdrop-filter: blur(10px);
}

.contact-icon {
  color: #f093fb;
  margin-bottom: 24px;
}

.contact-card h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #ffffff;
}

.contact-card p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 24px;
}

.contact-email {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: #ffffff;
  text-decoration: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.contact-email:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-section {
    height: 300px;
  }
  
  .content-container {
    padding: 40px 16px;
  }
  
  .terms-section {
    padding: 24px;
  }
  
  .section-header h2 {
    font-size: 1.5rem;
  }
  
  .terms-item {
    flex-direction: column;
    text-align: center;
  }
  
  .privacy-grid {
    grid-template-columns: 1fr;
  }
  
  .contact-card {
    padding: 32px 24px;
  }
  
  .responsibility-item {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .terms-section {
    padding: 20px;
  }
  
  .contact-card {
    padding: 24px 20px;
  }
  
  .responsibility-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}
</style> 