import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import axios from "axios";
import "./style.css";
import "./assets/global-fonts.css";

// ✅ Set baseURL
axios.defaults.baseURL =
  "https://cinema-resavation-system-production-6ffc.up.railway.app";

// ✅ Gán sẵn token nếu có
const token = localStorage.getItem("token");
if (token) {
  axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
}

const app = createApp(App);
app.use(router);
app.mount("#app");
