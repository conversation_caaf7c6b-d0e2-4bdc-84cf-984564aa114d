import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { fileURLToPath, URL } from "node:url";

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    proxy: {
      "/api": {
        target:
          "https://cinema-resavation-system-production-6ffc.up.railway.app",
        changeOrigin: true,
      },
      "/uploads": {
        target:
          "https://cinema-resavation-system-production-6ffc.up.railway.app",
        changeOrigin: true,
      },
    },
  },
});
