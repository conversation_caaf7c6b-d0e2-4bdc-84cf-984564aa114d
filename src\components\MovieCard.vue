<template>
  <div class="movie-card">
    <div class="movie-poster">
      <img :src="movie.posterUrl || movie.poster" :alt="movie.tenPhim || movie.title" />
    </div>
    
    <div class="movie-info">
      <h3 class="movie-title">{{ movie.tenPhim || movie.title }}</h3>
      <p class="movie-genre">{{ movie.theLoai || movie.genre }}</p>
      <p class="movie-duration">{{ movie.thoiLuong || movie.duration }} phút</p>
      <p class="movie-rating">{{ movie.danhGia || movie.rating }}/10</p>
    </div>
    
    <div class="movie-actions">
      <button class="btn-book" @click="startBooking">
        Đặt vé
      </button>
      <button class="btn-details" @click="viewDetails">
        Chi tiết
      </button>
    </div>

    <!-- Booking Flow Component -->
    <MovieBookingFlow
      ref="bookingFlow"
      :movie="movie"
      @selectShowtime="onShowtimeSelected"
      @close="onBookingClose"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MovieBookingFlow from './MovieBookingFlow.vue'

const props = defineProps({
  movie: { type: Object, required: true }
})

const emit = defineEmits(['selectShowtime', 'viewDetails'])

const bookingFlow = ref(null)

function startBooking() {
  if (bookingFlow.value) {
    bookingFlow.value.startBookingFlow(props.movie)
  }
}

function viewDetails() {
  emit('viewDetails', props.movie)
}

function onShowtimeSelected(bookingData) {
  emit('selectShowtime', bookingData)
  console.log('Booking data:', bookingData)
  // Redirect to seat selection or booking confirmation
}

function onBookingClose() {
  console.log('Booking flow closed')
}
</script>

<style scoped>
.movie-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.movie-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.movie-poster {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.movie-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.movie-info {
  padding: 16px;
}

.movie-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.movie-genre {
  font-size: 14px;
  color: #666;
  margin: 0 0 4px 0;
}

.movie-duration {
  font-size: 14px;
  color: #888;
  margin: 0 0 4px 0;
}

.movie-rating {
  font-size: 14px;
  color: #f39c12;
  font-weight: 600;
  margin: 0;
}

.movie-actions {
  padding: 16px;
  display: flex;
  gap: 8px;
}

.btn-book {
  flex: 1;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-book:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-details {
  flex: 1;
  background: transparent;
  color: #007bff;
  border: 2px solid #007bff;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-details:hover {
  background: #007bff;
  color: white;
  transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
  .movie-actions {
    flex-direction: column;
  }
  
  .btn-book, .btn-details {
    flex: none;
  }
}
</style>
