import axios from "axios";

const BASE_URL =
  "https://cinema-resavation-system-production-6ffc.up.railway.app/api";

/**
 * L<PERSON>y chi tiết suất chiếu theo ID
 * @param {string|number} scheduleId
 */
export async function fetchScheduleById(scheduleId) {
  return axios.get(`${BASE_URL}/suat-chieu/${scheduleId}`);
}

/**
 * L<PERSON>y danh sách ghế theo phòng chiếu (từ suất chiếu đã lấy idPhòng)
 * @param {string|number} roomId
 */
export async function fetchSeatsByRoom(roomId) {
  return axios.get(`${BASE_URL}/ghengoi/phongchieu/${roomId}`);
}

/**
 * Tạo hóa đơn / đặt vé
 * @param {Object} payload
 */
export async function createPOSBill(payload) {
  return axios.post(`${BASE_URL}/bill`, payload);
}
