{"name": "cinema-frontend-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "chart.js": "^4.5.0", "emailjs-com": "^3.2.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "vue": "^3.5.17", "vue-datepicker-next": "^1.0.3", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "vite": "^5.0.0"}}