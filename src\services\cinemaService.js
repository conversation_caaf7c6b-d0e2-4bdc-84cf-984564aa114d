import api from './api'
import { CINEMA_ENDPOINTS } from '../constants/api'

// L<PERSON><PERSON> danh sách tất cả rạp
export async function fetchCinemas(movieId = null) {
  try {
    let url = CINEMA_ENDPOINTS.GET_ALL
    
    // Nếu có movieId, thêm vào query để lấy rạp có lịch chiếu cho phim đó
    if (movieId) {
      url += `?movieId=${movieId}`
    }
    
    const response = await api.get(url)
    return response.data
  } catch (error) {
    console.error('Error fetching cinemas:', error)
    throw error
  }
}

// Lấy thông tin chi tiết 1 rạp
export async function getCinemaById(cinemaId) {
  try {
    const url = CINEMA_ENDPOINTS.GET_BY_ID.replace('{id}', cinemaId)
    const response = await api.get(url)
    return response.data
  } catch (error) {
    console.error('Error fetching cinema by ID:', error)
    throw error
  }
}

// L<PERSON>y danh sách phòng của 1 rạp
export async function getCinemaRooms(cinemaId) {
  try {
    const url = CINEMA_ENDPOINTS.GET_ROOMS.replace('{id}', cinemaId)
    const response = await api.get(url)
    return response.data
  } catch (error) {
    console.error('Error fetching cinema rooms:', error)
    throw error
  }
}
