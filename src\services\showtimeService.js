import api from "./api";
import { SHOWTIME_ENDPOINTS } from "../constants/api";

// L<PERSON>y lịch chiếu theo phim và rạp
export async function fetchShowtimesByMovieCinema(
  movieId,
  cinemaId,
  startDate = null,
  days = 7
) {
  try {
    let url = SHOWTIME_ENDPOINTS.GET_BY_MOVIE_CINEMA.replace(
      "{movieId}",
      movieId
    ).replace("{cinemaId}", cinemaId);

    const params = new URLSearchParams();
    if (startDate) params.append("startDate", startDate);
    params.append("days", days);

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching showtimes:", error);
    throw error;
  }
}

// L<PERSON>y lịch chiếu theo phim (tất cả rạp)
export async function fetchShowtimesByMovie(
  movieId,
  startDate = null,
  days = 7
) {
  try {
    let url = SHOWTIME_ENDPOINTS.GET_BY_MOVIE.replace("{movieId}", movieId);

    const params = new URLSearchParams();
    if (startDate) params.append("startDate", startDate);
    params.append("days", days);

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching showtimes by movie:", error);
    throw error;
  }
}

// Lấy danh sách ngày có lịch chiếu
export async function fetchAvailableDates(movieId, cinemaId = null) {
  try {
    let url = SHOWTIME_ENDPOINTS.GET_AVAILABLE_DATES;

    const params = new URLSearchParams();
    params.append("movieId", movieId);
    if (cinemaId) params.append("cinemaId", cinemaId);

    url += `?${params.toString()}`;

    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error("Error fetching available dates:", error);
    throw error;
  }
}
